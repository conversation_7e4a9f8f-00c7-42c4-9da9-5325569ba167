/**
 * Glowess City E-commerce Admin Styles
 */

/* City Gallery Meta Box */
.city-image-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 15px;
    background: #f9f9f9;
    border-radius: 5px;
}

.city-image-item h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.city-image-item .image-preview {
    text-align: center;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.city-image-item .image-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 3px;
}

.city-image-item .button {
    margin-right: 10px;
}

.city-image-item input[type="text"],
.city-image-item textarea {
    width: 100%;
    margin-top: 5px;
}

.city-image-item label {
    font-weight: 600;
    color: #333;
}

/* City Statistics */
.city-statistics {
    font-size: 13px;
}

.city-statistics p {
    margin: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.city-stat-number {
    font-size: 16px;
    font-weight: bold;
    color: #0073aa;
}

.city-stat-date {
    font-style: italic;
    color: #666;
}

/* City Slug Info */
.city-slug-info {
    background: #f1f1f1;
    border-left: 4px solid #0073aa;
    padding: 10px;
    margin: 10px 0;
    border-radius: 0 3px 3px 0;
}

.city-slug-info p {
    margin: 0;
    font-size: 13px;
}

.city-slug-info code {
    background: #fff;
    padding: 2px 5px;
    border-radius: 2px;
    font-size: 12px;
}

/* Admin Columns */
.column-city_slug,
.column-city_images,
.column-city_region,
.column-product_city,
.column-category_city {
    width: 120px;
}

.column-city_images {
    text-align: center;
}

/* Settings Page */
.glowess-city-settings {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.glowess-city-settings form {
    flex: 2;
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.glowess-city-sidebar {
    flex: 1;
}

.glowess-city-sidebar .postbox {
    margin-bottom: 20px;
}

.glowess-city-sidebar .postbox h3 {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    background: #f1f1f1;
    border-bottom: 1px solid #ddd;
}

.glowess-city-sidebar .inside {
    padding: 12px;
}

.glowess-city-sidebar ol {
    padding-left: 20px;
    margin: 0;
}

.glowess-city-sidebar ol li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.glowess-city-sidebar code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    color: #d63384;
}

.glowess-city-sidebar p {
    margin: 8px 0;
    font-size: 13px;
    line-height: 1.4;
}

.glowess-city-sidebar strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

/* Form Table Improvements */
.form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
}

.form-table td {
    padding: 15px 10px;
}

.form-table .description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
}

/* Color Picker */
input[type="color"] {
    width: 50px;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notices */
.notice.glowess-city-notice {
    border-left-color: #0073aa;
}

.notice.glowess-city-notice .notice-title {
    font-weight: 600;
    color: #0073aa;
}

/* Media Upload Buttons */
.city-image-item .select-image {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.city-image-item .select-image:hover {
    background: #005a87;
    border-color: #005a87;
}

.city-image-item .remove-image {
    background: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.city-image-item .remove-image:hover {
    background: #c82333;
    border-color: #c82333;
}

/* Responsive Admin */
@media screen and (max-width: 782px) {
    .glowess-city-settings {
        flex-direction: column;
    }
    
    .glowess-city-settings form,
    .glowess-city-sidebar {
        flex: none;
    }
    
    .form-table th,
    .form-table td {
        display: block;
        width: auto;
        padding: 10px 0;
    }
    
    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .city-image-item {
        padding: 10px;
    }
    
    .city-image-item .button {
        display: block;
        margin: 5px 0;
        text-align: center;
    }
}

/* WordPress Admin Color Schemes */
.admin-color-fresh .city-stat-number,
.admin-color-fresh .city-slug-info {
    border-left-color: #0073aa;
}

.admin-color-fresh .city-stat-number {
    color: #0073aa;
}

.admin-color-light .city-stat-number,
.admin-color-light .city-slug-info {
    border-left-color: #04a4cc;
}

.admin-color-light .city-stat-number {
    color: #04a4cc;
}

.admin-color-blue .city-stat-number,
.admin-color-blue .city-slug-info {
    border-left-color: #096484;
}

.admin-color-blue .city-stat-number {
    color: #096484;
}

.admin-color-coffee .city-stat-number,
.admin-color-coffee .city-slug-info {
    border-left-color: #c7a589;
}

.admin-color-coffee .city-stat-number {
    color: #c7a589;
}

.admin-color-ectoplasm .city-stat-number,
.admin-color-ectoplasm .city-slug-info {
    border-left-color: #a3b745;
}

.admin-color-ectoplasm .city-stat-number {
    color: #a3b745;
}

.admin-color-midnight .city-stat-number,
.admin-color-midnight .city-slug-info {
    border-left-color: #e14d43;
}

.admin-color-midnight .city-stat-number {
    color: #e14d43;
}

.admin-color-ocean .city-stat-number,
.admin-color-ocean .city-slug-info {
    border-left-color: #627c83;
}

.admin-color-ocean .city-stat-number {
    color: #627c83;
}

.admin-color-sunrise .city-stat-number,
.admin-color-sunrise .city-slug-info {
    border-left-color: #dd823b;
}

.admin-color-sunrise .city-stat-number {
    color: #dd823b;
}
