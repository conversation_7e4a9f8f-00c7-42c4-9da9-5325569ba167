<?php
/**
 * City Template
 * 
 * Template for displaying city pages
 * 
 * @package Glowess City Ecommerce
 */

get_header();

$city_slug = get_query_var('city_slug');
$city_post = null;

if ($city_slug) {
    $cities = get_posts(array(
        'post_type' => 'city',
        'name' => $city_slug,
        'post_status' => 'publish',
        'posts_per_page' => 1
    ));
    
    if (!empty($cities)) {
        $city_post = $cities[0];
    }
}
?>

<div class="city-page-container">
    <?php if ($city_post): ?>
        <div class="city-header">
            <h1 class="city-title"><?php echo esc_html($city_post->post_title); ?></h1>
            
            <?php if (has_post_thumbnail($city_post->ID)): ?>
                <div class="city-featured-image">
                    <?php echo get_the_post_thumbnail($city_post->ID, 'large'); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($city_post->post_content): ?>
                <div class="city-description">
                    <?php echo wp_kses_post($city_post->post_content); ?>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if (function_exists('woocommerce_content')): ?>
            <div class="city-products">
                <h2><?php echo sprintf(__('%s Ürünleri', 'glowess-city-ecommerce'), esc_html($city_post->post_title)); ?></h2>
                
                <?php
                // Query products for this city
                $args = array(
                    'post_type' => 'product',
                    'posts_per_page' => get_option('posts_per_page'),
                    'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
                    'meta_query' => array(
                        array(
                            'key' => '_city',
                            'value' => $city_slug,
                            'compare' => '='
                        )
                    )
                );
                
                $products_query = new WP_Query($args);
                
                if ($products_query->have_posts()): ?>
                    <div class="woocommerce">
                        <div class="products-grid">
                            <?php
                            woocommerce_product_loop_start();
                            
                            while ($products_query->have_posts()): 
                                $products_query->the_post();
                                wc_get_template_part('content', 'product');
                            endwhile;
                            
                            woocommerce_product_loop_end();
                            ?>
                        </div>
                        
                        <?php
                        // Pagination
                        $pagination_args = array(
                            'total' => $products_query->max_num_pages,
                            'current' => max(1, get_query_var('paged')),
                            'base' => '/' . $city_slug . '/page/%#%/',
                            'format' => ''
                        );
                        
                        echo paginate_links($pagination_args);
                        ?>
                    </div>
                <?php else: ?>
                    <p><?php echo sprintf(__('%s için henüz ürün bulunmamaktadır.', 'glowess-city-ecommerce'), esc_html($city_post->post_title)); ?></p>
                <?php endif; 
                
                wp_reset_postdata();
                ?>
            </div>
        <?php endif; ?>
        
    <?php else: ?>
        <div class="city-not-found">
            <h1><?php _e('Şehir Bulunamadı', 'glowess-city-ecommerce'); ?></h1>
            <p><?php _e('Aradığınız şehir bulunamadı.', 'glowess-city-ecommerce'); ?></p>
        </div>
    <?php endif; ?>
</div>

<style>
.city-page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.city-header {
    margin-bottom: 40px;
    text-align: center;
}

.city-title {
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #333;
}

.city-featured-image {
    margin-bottom: 20px;
}

.city-featured-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

.city-description {
    font-size: 1.1em;
    line-height: 1.6;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

.city-products h2 {
    font-size: 2em;
    margin-bottom: 30px;
    text-align: center;
    color: #333;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.city-not-found {
    text-align: center;
    padding: 60px 20px;
}

.city-not-found h1 {
    font-size: 2em;
    color: #333;
    margin-bottom: 20px;
}

.city-not-found p {
    font-size: 1.1em;
    color: #666;
}

@media (max-width: 768px) {
    .city-title {
        font-size: 2em;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }
}
</style>

<?php get_footer(); ?>