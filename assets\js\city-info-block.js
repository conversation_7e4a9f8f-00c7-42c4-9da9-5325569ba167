(function() {
    'use strict';

    var registerBlockType = wp.blocks.registerBlockType;
    var InspectorControls = wp.blockEditor.InspectorControls;
    var useBlockProps = wp.blockEditor.useBlockProps;
    var PanelBody = wp.components.PanelBody;
    var SelectControl = wp.components.SelectControl;
    var ToggleControl = wp.components.ToggleControl;
    var Placeholder = wp.components.Placeholder;
    var Spinner = wp.components.Spinner;
    var useState = wp.element.useState;
    var useEffect = wp.element.useEffect;
    var createElement = wp.element.createElement;
    var Fragment = wp.element.Fragment;

    function CityInfoEdit(props) {
        var attributes = props.attributes;
        var setAttributes = props.setAttributes;
        var citySlug = attributes.citySlug;
        var showTitle = attributes.showTitle;
        var showDescription = attributes.showDescription;
        var useUrlParameter = attributes.useUrlParameter;

        var citiesState = useState([]);
        var cities = citiesState[0];
        var setCities = citiesState[1];

        var loadingState = useState(true);
        var loading = loadingState[0];
        var setLoading = loadingState[1];

        var previewState = useState('');
        var preview = previewState[0];
        var setPreview = previewState[1];

        // Load cities on component mount
        useEffect(function() {
            loadCities();
        }, []);

        // Update preview when attributes change
        useEffect(function() {
            updatePreview();
        }, [attributes, cities]);

        function loadCities() {
            var formData = new FormData();
            formData.append('action', 'get_cities_list');
            formData.append('nonce', window.cityInfoBlock ? window.cityInfoBlock.nonce : '');

            fetch(window.cityInfoBlock ? window.cityInfoBlock.ajaxUrl : '/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(result) {
                if (result.success) {
                    setCities([
                        { value: '', label: 'URL parametresini kullan' }
                    ].concat(result.data));
                }
                setLoading(false);
            })
            .catch(function(error) {
                console.error('Error loading cities:', error);
                setLoading(false);
            });
        }

        function updatePreview() {
            var newPreview = '';

            if (useUrlParameter && !citySlug) {
                newPreview = 'URL parametresinden şehir bilgisi gösterilecek (?city=sehir-slug)';
            } else if (citySlug) {
                var selectedCity = cities.find(function(city) {
                    return city.value === citySlug;
                });
                if (selectedCity) {
                    newPreview = 'Şehir: ' + selectedCity.label;

                    var showParts = [];
                    if (showTitle) showParts.push('Başlık');
                    if (showDescription) showParts.push('Açıklama');

                    if (showParts.length > 0) {
                        newPreview += '\nGösterilecek: ' + showParts.join(', ');
                    }
                }
            } else {
                newPreview = 'Şehir seçin veya URL parametresi kullanın';
            }

            setPreview(newPreview);
        }

        var blockProps = useBlockProps({
            className: 'city-info-block-editor'
        });

        return createElement(Fragment, null,
            createElement(InspectorControls, null,
                createElement(PanelBody, {
                    title: 'Şehir Ayarları',
                    initialOpen: true
                },
                    createElement(ToggleControl, {
                        label: 'URL parametresini kullan',
                        help: 'Aktifse ?city=sehir-slug parametresini kullanır',
                        checked: useUrlParameter,
                        onChange: function(value) {
                            setAttributes({ useUrlParameter: value });
                        }
                    }),

                    !loading && createElement(SelectControl, {
                        label: 'Şehir Seçin',
                        help: useUrlParameter ? 'URL parametresi yoksa bu şehir gösterilir' : 'Gösterilecek şehir',
                        value: citySlug,
                        options: cities,
                        onChange: function(value) {
                            setAttributes({ citySlug: value });
                        }
                    })
                ),

                createElement(PanelBody, {
                    title: 'Gösterim Ayarları',
                    initialOpen: true
                },
                    createElement(ToggleControl, {
                        label: 'Başlığı Göster',
                        checked: showTitle,
                        onChange: function(value) {
                            setAttributes({ showTitle: value });
                        }
                    }),

                    createElement(ToggleControl, {
                        label: 'Açıklamayı Göster',
                        checked: showDescription,
                        onChange: function(value) {
                            setAttributes({ showDescription: value });
                        }
                    })
                )
            ),

            createElement('div', blockProps,
                loading ?
                    createElement(Placeholder, {
                        icon: 'location-alt',
                        label: 'Şehir Bilgileri'
                    },
                        createElement(Spinner),
                        createElement('p', null, 'Şehirler yükleniyor...')
                    ) :
                    createElement(Placeholder, {
                        icon: 'location-alt',
                        label: 'Şehir Bilgileri',
                        instructions: 'Sağ panelden ayarları yapılandırın'
                    },
                        createElement('div', {
                            className: 'city-info-preview'
                        },
                            createElement('strong', null, 'Önizleme:'),
                            createElement('pre', {
                                style: {
                                    whiteSpace: 'pre-wrap',
                                    marginTop: '10px',
                                    fontSize: '14px',
                                    background: '#f0f0f0',
                                    padding: '10px',
                                    borderRadius: '4px'
                                }
                            }, preview)
                        )
                    )
            )
        );
    }

    registerBlockType('glowess/city-info', {
        apiVersion: 2,
        title: 'Şehir Bilgileri',
        description: 'Şehir başlığı ve açıklamasını gösterir',
        icon: 'location-alt',
        category: 'widgets',
        keywords: ['şehir', 'city', 'bilgi', 'info'],
        supports: {
            align: ['wide', 'full'],
            html: false
        },
        attributes: {
            citySlug: {
                type: 'string',
                default: ''
            },
            showTitle: {
                type: 'boolean',
                default: true
            },
            showDescription: {
                type: 'boolean',
                default: true
            },
            useUrlParameter: {
                type: 'boolean',
                default: true
            }
        },
        edit: CityInfoEdit,
        save: function() {
            return null;
        }
    });
})();
