<?php
/**
 * City Post Type Class
 * 
 * Handles the registration and management of the 'city' custom post type
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_City_Post_Type {
    
    /**
     * Post type name
     */
    const POST_TYPE = 'city';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_post_type'), 5);
        add_action('init', array($this, 'register_taxonomies'), 6);
        add_action('init', array($this, 'add_custom_rewrite_rules'), 10);
        add_filter('post_updated_messages', array($this, 'updated_messages'));
        add_filter('bulk_post_updated_messages', array($this, 'bulk_updated_messages'), 10, 2);
        
        // Add custom columns to admin list
        add_filter('manage_' . self::POST_TYPE . '_posts_columns', array($this, 'add_custom_columns'));
        add_action('manage_' . self::POST_TYPE . '_posts_custom_column', array($this, 'custom_column_content'), 10, 2);
        add_filter('manage_edit-' . self::POST_TYPE . '_sortable_columns', array($this, 'sortable_columns'));
        
        // Add meta boxes
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
        
        // Add image support
        add_action('init', array($this, 'add_image_support'));
        
        // Handle city URL routing
        add_action('template_redirect', array($this, 'handle_city_routing'));
        add_filter('query_vars', array($this, 'add_query_vars'));
        
        // Flush rewrite rules when cities are added/updated/deleted
        add_action('save_post_' . self::POST_TYPE, array($this, 'flush_rewrite_rules_on_city_change'));
        add_action('delete_post', array($this, 'flush_rewrite_rules_on_city_delete'));
        add_action('wp_trash_post', array($this, 'flush_rewrite_rules_on_city_delete'));
        add_action('untrash_post', array($this, 'flush_rewrite_rules_on_city_change'));
        
        // Add template redirect for city pages
        add_action('template_include', array($this, 'city_template_include'));
    }
    
    /**
     * Register the city post type
     */
    public function register_post_type() {
        $labels = array(
            'name'                  => _x('Şehirler', 'Post type general name', 'glowess-city-ecommerce'),
            'singular_name'         => _x('Şehir', 'Post type singular name', 'glowess-city-ecommerce'),
            'menu_name'             => _x('Şehirler', 'Admin Menu text', 'glowess-city-ecommerce'),
            'name_admin_bar'        => _x('Şehir', 'Add New on Toolbar', 'glowess-city-ecommerce'),
            'add_new'               => __('Yeni Ekle', 'glowess-city-ecommerce'),
            'add_new_item'          => __('Yeni Şehir Ekle', 'glowess-city-ecommerce'),
            'new_item'              => __('Yeni Şehir', 'glowess-city-ecommerce'),
            'edit_item'             => __('Şehir Düzenle', 'glowess-city-ecommerce'),
            'view_item'             => __('Şehir Görüntüle', 'glowess-city-ecommerce'),
            'all_items'             => __('Tüm Şehirler', 'glowess-city-ecommerce'),
            'search_items'          => __('Şehir Ara', 'glowess-city-ecommerce'),
            'parent_item_colon'     => __('Üst Şehir:', 'glowess-city-ecommerce'),
            'not_found'             => __('Şehir bulunamadı.', 'glowess-city-ecommerce'),
            'not_found_in_trash'    => __('Çöp kutusunda şehir bulunamadı.', 'glowess-city-ecommerce'),
            'featured_image'        => _x('Şehir Görseli', 'Overrides the "Featured Image" phrase', 'glowess-city-ecommerce'),
            'set_featured_image'    => _x('Şehir görseli seç', 'Overrides the "Set featured image" phrase', 'glowess-city-ecommerce'),
            'remove_featured_image' => _x('Şehir görselini kaldır', 'Overrides the "Remove featured image" phrase', 'glowess-city-ecommerce'),
            'use_featured_image'    => _x('Şehir görseli olarak kullan', 'Overrides the "Use as featured image" phrase', 'glowess-city-ecommerce'),
            'archives'              => _x('Şehir arşivleri', 'The post type archive label', 'glowess-city-ecommerce'),
            'insert_into_item'      => _x('Şehire ekle', 'Overrides the "Insert into post" phrase', 'glowess-city-ecommerce'),
            'uploaded_to_this_item' => _x('Bu şehire yüklendi', 'Overrides the "Uploaded to this post" phrase', 'glowess-city-ecommerce'),
            'filter_items_list'     => _x('Şehir listesini filtrele', 'Screen reader text for the filter links', 'glowess-city-ecommerce'),
            'items_list_navigation' => _x('Şehir listesi navigasyonu', 'Screen reader text for the pagination', 'glowess-city-ecommerce'),
            'items_list'            => _x('Şehir listesi', 'Screen reader text for the items list', 'glowess-city-ecommerce'),
        );
        
        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_admin_bar'  => true,
            'show_in_nav_menus'  => true,
            'can_export'         => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'sehir', 'with_front' => false),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => 25,
            'menu_icon'          => 'dashicons-location-alt',
            'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'show_in_rest'       => true,
            'rest_base'          => 'cities',
            'rest_controller_class' => 'WP_REST_Posts_Controller',
        );
        
        register_post_type(self::POST_TYPE, $args);
    }
    
    /**
     * Register taxonomies for cities if needed
     */
    public function register_taxonomies() {
        // City regions taxonomy
        $labels = array(
            'name'              => _x('Bölgeler', 'taxonomy general name', 'glowess-city-ecommerce'),
            'singular_name'     => _x('Bölge', 'taxonomy singular name', 'glowess-city-ecommerce'),
            'search_items'      => __('Bölge Ara', 'glowess-city-ecommerce'),
            'all_items'         => __('Tüm Bölgeler', 'glowess-city-ecommerce'),
            'parent_item'       => __('Üst Bölge', 'glowess-city-ecommerce'),
            'parent_item_colon' => __('Üst Bölge:', 'glowess-city-ecommerce'),
            'edit_item'         => __('Bölge Düzenle', 'glowess-city-ecommerce'),
            'update_item'       => __('Bölge Güncelle', 'glowess-city-ecommerce'),
            'add_new_item'      => __('Yeni Bölge Ekle', 'glowess-city-ecommerce'),
            'new_item_name'     => __('Yeni Bölge Adı', 'glowess-city-ecommerce'),
            'menu_name'         => __('Bölgeler', 'glowess-city-ecommerce'),
        );
        
        $args = array(
            'hierarchical'      => true,
            'labels'            => $labels,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array('slug' => 'bolge'),
            'show_in_rest'      => true,
        );
        
        register_taxonomy('city_region', array(self::POST_TYPE), $args);
    }
    
    /**
     * Add image support
     */
    public function add_image_support() {
        add_theme_support('post-thumbnails', array(self::POST_TYPE));
    }
    
    /**
     * Add custom rewrite rules for city URLs
     */
    public function add_custom_rewrite_rules() {
        // Get all published cities to create specific rules
        $cities = get_posts(array(
            'post_type' => self::POST_TYPE,
            'post_status' => 'publish',
            'posts_per_page' => -1
        ));
        
        // Create specific rewrite rules for each city
        foreach ($cities as $city_post) {
            $city_slug = $city_post->post_name;
            
            // Add rewrite rule for city pages: /antalya/ -> ?city_slug=antalya
            add_rewrite_rule(
                '^' . preg_quote($city_slug, '/') . '/?$',
                'index.php?city_slug=' . $city_slug,
                'top'
            );
            
            // Add rewrite rule for city with pagination: /antalya/page/2/
            add_rewrite_rule(
                '^' . preg_quote($city_slug, '/') . '/page/([0-9]+)/?$',
                'index.php?city_slug=' . $city_slug . '&paged=$matches[1]',
                'top'
            );
        }
        
        // Fallback generic rule for new cities that might not be cached yet
        add_rewrite_rule(
            '^([a-zA-Z0-9-_]+)/?$',
            'index.php?city_slug=$matches[1]',
            'bottom'
        );
        
        add_rewrite_rule(
            '^([a-zA-Z0-9-_]+)/page/([0-9]+)/?$',
            'index.php?city_slug=$matches[1]&paged=$matches[2]',
            'bottom'
        );
    }
    
    /**
     * Add custom query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'city_slug';
        return $vars;
    }
    
    /**
      * Handle city URL routing
      */
     public function handle_city_routing() {
         $city_slug = get_query_var('city_slug');
         
         if (!$city_slug) {
             return;
         }
         
         // Check if this is a valid city slug
         $city = get_posts(array(
             'post_type' => self::POST_TYPE,
             'name' => $city_slug,
             'post_status' => 'publish',
             'posts_per_page' => 1
         ));
         
         if (empty($city)) {
             // If not a valid city, let WordPress handle it normally
             return;
         }
         
         // Set the city parameter for other parts of the system
         $_GET['city'] = $city_slug;
         
         // Handle different page types
         global $wp_query;
         
         // Set up the main query for city page
         $wp_query->set('post_type', array('post', 'product'));
         $wp_query->is_home = false;
         $wp_query->is_archive = true;
         $wp_query->is_singular = false;
         $wp_query->is_page = false;
         
         // Set queried object to the city post
         $wp_query->queried_object = $city[0];
         $wp_query->queried_object_id = $city[0]->ID;
         
         // Add city filter to queries
         add_filter('pre_get_posts', array($this, 'modify_main_query_for_city'));
         
         // If WooCommerce is active, add product filtering
         if (function_exists('wc_get_page_id')) {
             add_filter('woocommerce_product_query_meta_query', array($this, 'add_city_meta_query'));
         }
     }
     
     /**
      * Modify main query for city pages
      */
     public function modify_main_query_for_city($query) {
         if (!is_admin() && $query->is_main_query()) {
             $city_slug = get_query_var('city_slug');
             if ($city_slug) {
                 // Add meta query to filter by city
                 $meta_query = $query->get('meta_query');
                 if (!is_array($meta_query)) {
                     $meta_query = array();
                 }
                 
                 $meta_query[] = array(
                     'key' => '_city',
                     'value' => $city_slug,
                     'compare' => '='
                 );
                 
                 $query->set('meta_query', $meta_query);
             }
         }
     }
     
     /**
      * Add city meta query to product queries
      */
     public function add_city_meta_query($meta_query) {
         $city_slug = get_query_var('city_slug');
         
         if ($city_slug) {
             $meta_query[] = array(
                 'key' => '_product_city',
                 'value' => $city_slug,
                 'compare' => 'LIKE'
             );
         }
         
         return $meta_query;
     }
     
     /**
      * Flush rewrite rules when city is changed
      */
     public function flush_rewrite_rules_on_city_change($post_id) {
         if (get_post_type($post_id) === self::POST_TYPE) {
             flush_rewrite_rules();
         }
     }
     
     /**
      * Flush rewrite rules when city is deleted
      */
     public function flush_rewrite_rules_on_city_delete($post_id) {
         if (get_post_type($post_id) === self::POST_TYPE) {
             flush_rewrite_rules();
         }
     }
     
     /**
      * Add custom columns to admin list
      */
    public function add_custom_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['city_slug'] = __('Slug', 'glowess-city-ecommerce');
        $new_columns['city_images'] = __('Görseller', 'glowess-city-ecommerce');
        $new_columns['city_region'] = __('Bölge', 'glowess-city-ecommerce');
        $new_columns['date'] = $columns['date'];
        
        return $new_columns;
    }
    
    /**
     * Custom column content
     */
    public function custom_column_content($column, $post_id) {
        switch ($column) {
            case 'city_slug':
                $post = get_post($post_id);
                echo esc_html($post->post_name);
                break;
                
            case 'city_images':
                $images = get_post_meta($post_id, '_city_gallery_images', true);
                if ($images && is_array($images)) {
                    echo count($images) . ' ' . __('görsel', 'glowess-city-ecommerce');
                } else {
                    echo __('Görsel yok', 'glowess-city-ecommerce');
                }
                break;
                
            case 'city_region':
                $terms = get_the_terms($post_id, 'city_region');
                if ($terms && !is_wp_error($terms)) {
                    $term_names = wp_list_pluck($terms, 'name');
                    echo esc_html(implode(', ', $term_names));
                } else {
                    echo __('Bölge atanmamış', 'glowess-city-ecommerce');
                }
                break;
        }
    }
    
    /**
     * Make columns sortable
     */
    public function sortable_columns($columns) {
        $columns['city_slug'] = 'post_name';
        return $columns;
    }
    
    /**
     * Updated messages for city post type
     */
    public function updated_messages($messages) {
        $post = get_post();
        
        $messages[self::POST_TYPE] = array(
            0  => '', // Unused. Messages start at index 1.
            1  => __('Şehir güncellendi.', 'glowess-city-ecommerce'),
            2  => __('Özel alan güncellendi.', 'glowess-city-ecommerce'),
            3  => __('Özel alan silindi.', 'glowess-city-ecommerce'),
            4  => __('Şehir güncellendi.', 'glowess-city-ecommerce'),
            5  => isset($_GET['revision']) ? sprintf(__('Şehir geri yüklendi. Revizyon: %s', 'glowess-city-ecommerce'), wp_post_revision_title((int) $_GET['revision'], false)) : false,
            6  => __('Şehir yayınlandı.', 'glowess-city-ecommerce'),
            7  => __('Şehir kaydedildi.', 'glowess-city-ecommerce'),
            8  => __('Şehir gönderildi.', 'glowess-city-ecommerce'),
            9  => sprintf(
                __('Şehir planlandı: <strong>%1$s</strong>.', 'glowess-city-ecommerce'),
                date_i18n(__('M j, Y @ G:i', 'glowess-city-ecommerce'), strtotime($post->post_date))
            ),
            10 => __('Şehir taslağı güncellendi.', 'glowess-city-ecommerce')
        );
        
        return $messages;
    }
    
    /**
     * Bulk updated messages
     */
    public function bulk_updated_messages($bulk_messages, $bulk_counts) {
        $bulk_messages[self::POST_TYPE] = array(
            'updated'   => _n('%s şehir güncellendi.', '%s şehir güncellendi.', $bulk_counts['updated'], 'glowess-city-ecommerce'),
            'locked'    => _n('%s şehir güncellenmedi, başka biri tarafından düzenleniyor.', '%s şehir güncellenmedi, başka biri tarafından düzenleniyor.', $bulk_counts['locked'], 'glowess-city-ecommerce'),
            'deleted'   => _n('%s şehir kalıcı olarak silindi.', '%s şehir kalıcı olarak silindi.', $bulk_counts['deleted'], 'glowess-city-ecommerce'),
            'trashed'   => _n('%s şehir çöp kutusuna taşındı.', '%s şehir çöp kutusuna taşındı.', $bulk_counts['trashed'], 'glowess-city-ecommerce'),
            'untrashed' => _n('%s şehir çöp kutusundan geri alındı.', '%s şehir çöp kutusundan geri alındı.', $bulk_counts['untrashed'], 'glowess-city-ecommerce'),
        );
        
        return $bulk_messages;
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'city-gallery-images',
            __('Şehir Galeri Görselleri (3 adet)', 'glowess-city-ecommerce'),
            array($this, 'gallery_images_meta_box'),
            self::POST_TYPE,
            'normal',
            'high'
        );
    }
    
    /**
     * Gallery images meta box
     */
    public function gallery_images_meta_box($post) {
        wp_nonce_field('city_gallery_images_nonce', 'city_gallery_images_nonce');
        
        $images = get_post_meta($post->ID, '_city_gallery_images', true);
        if (!is_array($images)) {
            $images = array();
        }
        
        ?>
        <div id="city-gallery-container">
            <p><?php _e('Hero banner için 3 adet görsel ekleyebilirsiniz.', 'glowess-city-ecommerce'); ?></p>
            
            <div id="city-gallery-images">
                <?php for ($i = 0; $i < 3; $i++) : ?>
                    <div class="city-image-item" data-index="<?php echo $i; ?>">
                        <h4><?php echo sprintf(__('Görsel %d', 'glowess-city-ecommerce'), $i + 1); ?></h4>
                        
                        <?php
                        $image_id = isset($images[$i]['id']) ? $images[$i]['id'] : '';
                        $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'medium') : '';
                        $title = isset($images[$i]['title']) ? $images[$i]['title'] : '';
                        $description = isset($images[$i]['description']) ? $images[$i]['description'] : '';
                        ?>
                        
                        <div class="image-preview" style="margin-bottom: 10px;">
                            <?php if ($image_url) : ?>
                                <img src="<?php echo esc_url($image_url); ?>" style="max-width: 200px; height: auto;" />
                            <?php endif; ?>
                        </div>
                        
                        <input type="hidden" name="city_gallery_images[<?php echo $i; ?>][id]" value="<?php echo esc_attr($image_id); ?>" class="image-id" />
                        
                        <button type="button" class="button select-image"><?php _e('Görsel Seç', 'glowess-city-ecommerce'); ?></button>
                        <button type="button" class="button remove-image" style="<?php echo $image_id ? '' : 'display:none;'; ?>"><?php _e('Kaldır', 'glowess-city-ecommerce'); ?></button>
                        
                        <p>
                            <label><?php _e('Başlık:', 'glowess-city-ecommerce'); ?></label><br>
                            <input type="text" name="city_gallery_images[<?php echo $i; ?>][title]" value="<?php echo esc_attr($title); ?>" style="width: 100%;" />
                        </p>
                        
                        <p>
                            <label><?php _e('Açıklama:', 'glowess-city-ecommerce'); ?></label><br>
                            <textarea name="city_gallery_images[<?php echo $i; ?>][description]" rows="3" style="width: 100%;"><?php echo esc_textarea($description); ?></textarea>
                        </p>
                        
                        <hr style="margin: 20px 0;">
                    </div>
                <?php endfor; ?>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            var mediaUploader;
            
            $('.select-image').on('click', function(e) {
                e.preventDefault();
                
                var button = $(this);
                var container = button.closest('.city-image-item');
                
                if (mediaUploader) {
                    mediaUploader.open();
                    return;
                }
                
                mediaUploader = wp.media({
                    title: '<?php _e("Görsel Seç", "glowess-city-ecommerce"); ?>',
                    button: {
                        text: '<?php _e("Seç", "glowess-city-ecommerce"); ?>'
                    },
                    multiple: false
                });
                
                mediaUploader.on('select', function() {
                    var attachment = mediaUploader.state().get('selection').first().toJSON();
                    
                    container.find('.image-id').val(attachment.id);
                    container.find('.image-preview').html('<img src="' + attachment.sizes.medium.url + '" style="max-width: 200px; height: auto;" />');
                    container.find('.remove-image').show();
                });
                
                mediaUploader.open();
            });
            
            $('.remove-image').on('click', function(e) {
                e.preventDefault();
                
                var container = $(this).closest('.city-image-item');
                container.find('.image-id').val('');
                container.find('.image-preview').html('');
                $(this).hide();
            });
        });
        </script>
        <?php
    }
    
    /**
     * Save meta boxes
     */
    public function save_meta_boxes($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['city_gallery_images_nonce']) || !wp_verify_nonce($_POST['city_gallery_images_nonce'], 'city_gallery_images_nonce')) {
            return;
        }
        
        // Check if user has permission to edit
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Save gallery images
        if (isset($_POST['city_gallery_images'])) {
            $images = array();
            foreach ($_POST['city_gallery_images'] as $image_data) {
                if (!empty($image_data['id'])) {
                    $images[] = array(
                        'id' => intval($image_data['id']),
                        'title' => sanitize_text_field($image_data['title']),
                        'description' => sanitize_textarea_field($image_data['description'])
                    );
                }
            }
            update_post_meta($post_id, '_city_gallery_images', $images);
        }
    }
    
    /**
     * Template include for city pages
     */
    public function city_template_include($template) {
        $city_slug = get_query_var('city_slug');
        
        if ($city_slug) {
            // Check if this is a valid city slug
            $city = get_posts(array(
                'post_type' => self::POST_TYPE,
                'name' => $city_slug,
                'post_status' => 'publish',
                'posts_per_page' => 1
            ));
            
            if (!empty($city)) {
                // Look for city-specific template files
                $templates = array(
                    'city-' . $city_slug . '.php',
                    'city.php',
                    'archive-city.php',
                    'archive.php',
                    'index.php'
                );
                
                $new_template = locate_template($templates);
                if ($new_template) {
                    return $new_template;
                }
            }
        }
        
        return $template;
    }
    
}
