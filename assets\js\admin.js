/**
 * Glowess City E-commerce Admin JavaScript
 */

(function($) {
    'use strict';

    // Admin object
    var GlowessCityAdmin = {
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.initMediaUploader();
            this.initColorPicker();
            this.initTooltips();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            // Settings form validation
            $(document).on('submit', '#glowess-city-settings-form', this.validateSettingsForm);
            
            // City meta box events
            $(document).on('click', '.select-image', this.selectImage);
            $(document).on('click', '.remove-image', this.removeImage);
            
            // Admin notices dismiss
            $(document).on('click', '.glowess-city-notice .notice-dismiss', this.dismissNotice);
            
            // Quick actions
            $(document).on('click', '.city-quick-edit', this.quickEdit);
            $(document).on('click', '.city-bulk-action', this.bulkAction);
        },
        
        /**
         * Initialize media uploader
         */
        initMediaUploader: function() {
            // Media uploader is handled in the PHP meta box
            // This function can be extended for additional media functionality
        },
        
        /**
         * Initialize color picker
         */
        initColorPicker: function() {
            if ($.fn.wpColorPicker) {
                $('input[type="color"]').wpColorPicker({
                    change: function(event, ui) {
                        // Handle color change
                        var $input = $(this);
                        var color = ui.color.toString();
                        $input.val(color);
                    }
                });
            }
        },
        
        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            // Add tooltips to help icons
            $('.help-tip').tooltip({
                content: function() {
                    return $(this).prop('title');
                },
                position: {
                    my: 'center bottom-20',
                    at: 'center top',
                    using: function(position, feedback) {
                        $(this).css(position);
                        $('<div>')
                            .addClass('arrow')
                            .addClass(feedback.vertical)
                            .addClass(feedback.horizontal)
                            .appendTo(this);
                    }
                }
            });
        },
        
        /**
         * Select image
         */
        selectImage: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $container = $button.closest('.city-image-item');
            var $preview = $container.find('.image-preview');
            var $idInput = $container.find('.image-id');
            var $removeButton = $container.find('.remove-image');
            
            // Create media uploader
            var mediaUploader = wp.media({
                title: 'Görsel Seç',
                button: {
                    text: 'Seç'
                },
                multiple: false
            });
            
            // Handle selection
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                
                // Update preview
                var imageUrl = attachment.sizes && attachment.sizes.medium ? 
                              attachment.sizes.medium.url : attachment.url;
                
                $preview.html('<img src="' + imageUrl + '" style="max-width: 200px; height: auto;" />');
                $idInput.val(attachment.id);
                $removeButton.show();
                
                // Update button text
                $button.text('Görseli Değiştir');
            });
            
            // Open uploader
            mediaUploader.open();
        },
        
        /**
         * Remove image
         */
        removeImage: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $container = $button.closest('.city-image-item');
            var $preview = $container.find('.image-preview');
            var $idInput = $container.find('.image-id');
            var $selectButton = $container.find('.select-image');
            
            // Confirm removal
            if (confirm('Bu görseli kaldırmak istediğinizden emin misiniz?')) {
                $preview.html('');
                $idInput.val('');
                $button.hide();
                $selectButton.text('Görsel Seç');
            }
        },
        
        /**
         * Validate settings form
         */
        validateSettingsForm: function(e) {
            var isValid = true;
            var errors = [];
            
            // Add validation rules here
            // Example: Check if required fields are filled
            
            if (!isValid) {
                e.preventDefault();
                alert('Lütfen aşağıdaki hataları düzeltin:\n' + errors.join('\n'));
            }
            
            return isValid;
        },
        
        /**
         * Dismiss notice
         */
        dismissNotice: function(e) {
            var $notice = $(this).closest('.notice');
            var noticeId = $notice.data('notice-id');
            
            if (noticeId) {
                // Send AJAX request to dismiss notice permanently
                $.post(ajaxurl, {
                    action: 'dismiss_glowess_city_notice',
                    notice_id: noticeId,
                    nonce: glowess_city_admin.nonce
                });
            }
        },
        
        /**
         * Quick edit functionality
         */
        quickEdit: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var cityId = $button.data('city-id');
            
            // Implementation for quick edit
            // This would open an inline edit form
        },
        
        /**
         * Bulk action functionality
         */
        bulkAction: function(e) {
            var action = $('#bulk-action-selector-top').val();
            var selectedItems = [];
            
            $('input[name="post[]"]:checked').each(function() {
                selectedItems.push($(this).val());
            });
            
            if (selectedItems.length === 0) {
                alert('Lütfen en az bir öğe seçin.');
                e.preventDefault();
                return false;
            }
            
            // Handle specific bulk actions
            switch (action) {
                case 'feature':
                    if (confirm('Seçili şehirleri öne çıkarmak istediğinizden emin misiniz?')) {
                        this.performBulkAction('feature', selectedItems);
                    }
                    e.preventDefault();
                    break;
                    
                case 'unfeature':
                    if (confirm('Seçili şehirlerin öne çıkarma durumunu kaldırmak istediğinizden emin misiniz?')) {
                        this.performBulkAction('unfeature', selectedItems);
                    }
                    e.preventDefault();
                    break;
            }
        },
        
        /**
         * Perform bulk action
         */
        performBulkAction: function(action, items) {
            var $form = $('#posts-filter');
            $form.addClass('loading');
            
            $.post(ajaxurl, {
                action: 'glowess_city_bulk_action',
                bulk_action: action,
                items: items,
                nonce: glowess_city_admin.nonce
            }, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('İşlem başarısız: ' + (response.data || 'Bilinmeyen hata'));
                    $form.removeClass('loading');
                }
            });
        },
        
        /**
         * Show loading state
         */
        showLoading: function($element) {
            $element.addClass('loading');
        },
        
        /**
         * Hide loading state
         */
        hideLoading: function($element) {
            $element.removeClass('loading');
        },
        
        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showNotice(message, 'success');
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            this.showNotice(message, 'error');
        },
        
        /**
         * Show notice
         */
        showNotice: function(message, type) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap h1').after($notice);
            
            // Auto dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut();
            }, 5000);
        },
        
        /**
         * Utility: Format number
         */
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        },
        
        /**
         * Utility: Validate email
         */
        validateEmail: function(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        /**
         * Utility: Validate URL
         */
        validateURL: function(url) {
            var re = /^https?:\/\/.+/;
            return re.test(url);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        GlowessCityAdmin.init();
    });
    
    // Make object globally available
    window.GlowessCityAdmin = GlowessCityAdmin;
    
})(jQuery);
