<?php
/**
 * City Info Class
 * 
 * Handles city information meta fields and shortcode display
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_City_Info {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Add meta boxes for city info
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_info_meta_boxes'));
        
        // Enqueue styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'city-info-details',
            __('Şehir Bilgileri', 'glowess-city-ecommerce'),
            array($this, 'info_details_meta_box'),
            'city',
            'normal',
            'high'
        );
    }
    
    /**
     * City info details meta box
     */
    public function info_details_meta_box($post) {
        wp_nonce_field('city_info_details_nonce', 'city_info_details_nonce');

        // Get existing values
        $city_title = get_post_meta($post->ID, '_city_info_title', true);
        $city_description = get_post_meta($post->ID, '_city_info_description', true);

        ?>
        <div id="city-info-container">
            <p><?php _e('Şehir hakkında başlık ve açıklama bilgilerini ekleyebilirsiniz.', 'glowess-city-ecommerce'); ?></p>

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="city_info_title"><?php _e('Şehir Başlığı', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_info_title" name="city_info_title"
                               value="<?php echo esc_attr($city_title); ?>"
                               class="large-text"
                               placeholder="<?php _e('Örn: Antalya - Türkiye\'nin İncisi', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehir için özel başlık', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="city_info_description"><?php _e('Şehir Açıklaması', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <?php
                        wp_editor($city_description, 'city_info_description', array(
                            'textarea_name' => 'city_info_description',
                            'media_buttons' => true,
                            'textarea_rows' => 10,
                            'teeny' => false,
                            'tinymce' => array(
                                'toolbar1' => 'bold,italic,underline,strikethrough,|,bullist,numlist,blockquote,|,link,unlink,|,undo,redo',
                                'toolbar2' => 'formatselect,|,forecolor,backcolor,|,alignleft,aligncenter,alignright,alignjustify,|,image,media'
                            ),
                            'quicktags' => true
                        ));
                        ?>
                        <p class="description"><?php _e('Şehir hakkında detaylı açıklama. HTML ve medya desteklenir.', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
            </table>
        </div>

        <style>
        #city-info-container .form-table th {
            width: 150px;
            vertical-align: top;
            padding-top: 15px;
        }

        #city-info-container .form-table td {
            padding-top: 10px;
        }

        #city_info_title {
            font-size: 16px;
            padding: 8px 12px;
        }
        </style>
        <?php
    }

    /**
     * Save info meta boxes
     */
    public function save_info_meta_boxes($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['city_info_details_nonce']) || !wp_verify_nonce($_POST['city_info_details_nonce'], 'city_info_details_nonce')) {
            return;
        }

        // Check if user has permission to edit
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check post type
        if (get_post_type($post_id) !== 'city') {
            return;
        }

        // Save city title
        if (isset($_POST['city_info_title'])) {
            $title = sanitize_text_field($_POST['city_info_title']);
            if (!empty($title)) {
                update_post_meta($post_id, '_city_info_title', $title);
            } else {
                delete_post_meta($post_id, '_city_info_title');
            }
        }

        // Save city description
        if (isset($_POST['city_info_description'])) {
            $description = wp_kses_post($_POST['city_info_description']);
            if (!empty($description)) {
                update_post_meta($post_id, '_city_info_description', $description);
            } else {
                delete_post_meta($post_id, '_city_info_description');
            }
        }
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Only enqueue on pages that might have the shortcode
        if (is_singular() || is_home() || is_front_page()) {
            wp_enqueue_style(
                'glowess-city-info',
                GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/css/city-info.css',
                array(),
                GLOWESS_CITY_ECOMMERCE_VERSION
            );
        }
    }

    /**
     * Render info shortcode
     */
    public function render_info_shortcode($atts) {
        // Parse attributes
        $atts = shortcode_atts(array(
            'city' => '',
            'show' => 'both' // title, description, both
        ), $atts, 'city_info');

        // Get city from parameter or shortcode attribute
        $city_slug = '';
        if (!empty($atts['city'])) {
            $city_slug = sanitize_text_field($atts['city']);
        } elseif (isset($_GET['city']) && !empty($_GET['city'])) {
            $city_slug = sanitize_text_field($_GET['city']);
        }

        if (empty($city_slug)) {
            return '<div class="city-info-notice">' . __('Şehir parametresi bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }

        // Get city data
        $cities = get_posts(array(
            'post_type' => 'city',
            'name' => $city_slug,
            'post_status' => 'publish',
            'posts_per_page' => 1
        ));

        if (empty($cities)) {
            return '<div class="city-info-notice">' . __('Şehir bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }

        $city = $cities[0];

        // Get meta data
        $city_title = get_post_meta($city->ID, '_city_info_title', true);
        $city_description = get_post_meta($city->ID, '_city_info_description', true);

        // Start output
        ob_start();
        ?>
        <div class="city-info-container">
            <?php if (($atts['show'] === 'title' || $atts['show'] === 'both') && !empty($city_title)) : ?>
                <div class="city-info-title-section">
                    <h2 class="city-info-title"><?php echo esc_html($city_title); ?></h2>
                </div>
            <?php endif; ?>

            <?php if (($atts['show'] === 'description' || $atts['show'] === 'both') && !empty($city_description)) : ?>
                <div class="city-info-description-section">
                    <div class="city-info-description">
                        <?php echo wp_kses_post($city_description); ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (empty($city_title) && empty($city_description)) : ?>
                <div class="city-info-notice">
                    <?php echo sprintf(__('%s için bilgi bulunamadı.', 'glowess-city-ecommerce'), esc_html($city->post_title)); ?>
                </div>
            <?php endif; ?>
        </div>
        <?php

        return ob_get_clean();
    }
}
