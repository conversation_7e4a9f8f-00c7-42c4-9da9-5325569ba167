<?php
/**
 * City Meta Boxes Class
 * 
 * Handles additional meta boxes for city post type
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_City_Meta_Boxes {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
        
        // Add custom fields to city edit screen
        add_action('edit_form_after_title', array($this, 'add_city_slug_info'));
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'city-seo-settings',
            __('SEO Ayarları', 'glowess-city-ecommerce'),
            array($this, 'seo_settings_meta_box'),
            'city',
            'normal',
            'default'
        );

        add_meta_box(
            'city-banner-settings',
            __('Banner Ayarları', 'glowess-city-ecommerce'),
            array($this, 'banner_settings_meta_box'),
            'city',
            'normal',
            'default'
        );
        
        add_meta_box(
            'city-display-settings',
            __('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'glowess-city-ecommerce'),
            array($this, 'display_settings_meta_box'),
            'city',
            'side',
            'default'
        );
        
        add_meta_box(
            'city-statistics',
            __('İstatistikler', 'glowess-city-ecommerce'),
            array($this, 'statistics_meta_box'),
            'city',
            'side',
            'default'
        );
    }
    
    /**
     * SEO settings meta box
     */
    public function seo_settings_meta_box($post) {
        wp_nonce_field('city_seo_settings_nonce', 'city_seo_settings_nonce');
        
        $meta_title = get_post_meta($post->ID, '_city_meta_title', true);
        $meta_description = get_post_meta($post->ID, '_city_meta_description', true);
        $meta_keywords = get_post_meta($post->ID, '_city_meta_keywords', true);
        
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="city_meta_title"><?php _e('Meta Başlık', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <input type="text" id="city_meta_title" name="city_meta_title" value="<?php echo esc_attr($meta_title); ?>" class="regular-text" />
                    <p class="description"><?php _e('Arama motorları için özel başlık. Boş bırakılırsa şehir adı kullanılır.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="city_meta_description"><?php _e('Meta Açıklama', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <textarea id="city_meta_description" name="city_meta_description" rows="3" class="large-text"><?php echo esc_textarea($meta_description); ?></textarea>
                    <p class="description"><?php _e('Arama motorları için açıklama. Maksimum 160 karakter önerilir.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="city_meta_keywords"><?php _e('Anahtar Kelimeler', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <input type="text" id="city_meta_keywords" name="city_meta_keywords" value="<?php echo esc_attr($meta_keywords); ?>" class="regular-text" />
                    <p class="description"><?php _e('Virgülle ayrılmış anahtar kelimeler.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * Display settings meta box
     */
    public function display_settings_meta_box($post) {
        wp_nonce_field('city_display_settings_nonce', 'city_display_settings_nonce');
        
        $featured_city = get_post_meta($post->ID, '_city_featured', true);
        $city_color = get_post_meta($post->ID, '_city_color', true);
        $show_in_homepage = get_post_meta($post->ID, '_city_show_in_homepage', true);
        $city_order = get_post_meta($post->ID, '_city_order', true);
        
        ?>
        <table class="form-table">
            <tr>
                <th scope="row"><?php _e('Öne Çıkan Şehir', 'glowess-city-ecommerce'); ?></th>
                <td>
                    <label>
                        <input type="checkbox" name="city_featured" value="1" <?php checked($featured_city, '1'); ?> />
                        <?php _e('Bu şehri öne çıkar', 'glowess-city-ecommerce'); ?>
                    </label>
                    <p class="description"><?php _e('Öne çıkan şehirler ana sayfada ve listelerde öncelikli gösterilir.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="city_color"><?php _e('Şehir Rengi', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <input type="color" id="city_color" name="city_color" value="<?php echo esc_attr($city_color ?: '#0073aa'); ?>" />
                    <p class="description"><?php _e('Şehir teması için kullanılacak ana renk.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row"><?php _e('Ana Sayfada Göster', 'glowess-city-ecommerce'); ?></th>
                <td>
                    <label>
                        <input type="checkbox" name="city_show_in_homepage" value="1" <?php checked($show_in_homepage, '1'); ?> />
                        <?php _e('Ana sayfada göster', 'glowess-city-ecommerce'); ?>
                    </label>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="city_order"><?php _e('Sıralama', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <input type="number" id="city_order" name="city_order" value="<?php echo esc_attr($city_order ?: '0'); ?>" min="0" step="1" />
                    <p class="description"><?php _e('Şehirlerin sıralanması için kullanılır. Küçük sayılar önce gösterilir.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>
        </table>
        <?php
    }
    
    /**
     * Statistics meta box
     */
    public function statistics_meta_box($post) {
        // Get statistics
        $product_count = $this->get_city_product_count($post->ID);
        $category_count = $this->get_city_category_count($post->ID);
        $last_product_date = $this->get_last_product_date($post->ID);
        
        ?>
        <div class="city-statistics">
            <p>
                <strong><?php _e('Toplam Ürün:', 'glowess-city-ecommerce'); ?></strong>
                <span class="city-stat-number"><?php echo intval($product_count); ?></span>
            </p>
            
            <p>
                <strong><?php _e('Toplam Kategori:', 'glowess-city-ecommerce'); ?></strong>
                <span class="city-stat-number"><?php echo intval($category_count); ?></span>
            </p>
            
            <?php if ($last_product_date) : ?>
                <p>
                    <strong><?php _e('Son Ürün Tarihi:', 'glowess-city-ecommerce'); ?></strong><br>
                    <span class="city-stat-date"><?php echo date_i18n(get_option('date_format'), strtotime($last_product_date)); ?></span>
                </p>
            <?php endif; ?>
            
            <p>
                <a href="<?php echo admin_url('edit.php?post_type=product&city_filter=' . $post->ID); ?>" class="button button-secondary">
                    <?php _e('Ürünleri Görüntüle', 'glowess-city-ecommerce'); ?>
                </a>
            </p>
        </div>
        
        <style>
        .city-statistics p {
            margin: 10px 0;
        }
        .city-stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #0073aa;
        }
        .city-stat-date {
            font-style: italic;
        }
        </style>
        <?php
    }
    
    /**
     * Add city slug info after title
     */
    public function add_city_slug_info($post) {
        if ($post->post_type !== 'city') {
            return;
        }
        
        ?>
        <div class="city-slug-info" style="margin: 10px 0; padding: 10px; background: #f1f1f1; border-left: 4px solid #0073aa;">
            <p style="margin: 0;">
                <strong><?php _e('Şehir Slug:', 'glowess-city-ecommerce'); ?></strong>
                <code><?php echo esc_html($post->post_name ?: __('Yayınlandıktan sonra oluşturulacak', 'glowess-city-ecommerce')); ?></code>
            </p>
            <?php if ($post->post_name) : ?>
                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                    <?php _e('URL parametresi:', 'glowess-city-ecommerce'); ?>
                    <code>?city=<?php echo esc_html($post->post_name); ?></code>
                </p>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Save meta boxes
     */
    public function save_meta_boxes($post_id) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Check post type
        if (get_post_type($post_id) !== 'city') {
            return;
        }
        
        // Save SEO settings
        if (isset($_POST['city_seo_settings_nonce']) && wp_verify_nonce($_POST['city_seo_settings_nonce'], 'city_seo_settings_nonce')) {
            if (isset($_POST['city_meta_title'])) {
                update_post_meta($post_id, '_city_meta_title', sanitize_text_field($_POST['city_meta_title']));
            }
            
            if (isset($_POST['city_meta_description'])) {
                update_post_meta($post_id, '_city_meta_description', sanitize_textarea_field($_POST['city_meta_description']));
            }
            
            if (isset($_POST['city_meta_keywords'])) {
                update_post_meta($post_id, '_city_meta_keywords', sanitize_text_field($_POST['city_meta_keywords']));
            }
        }
        
        // Save display settings
        if (isset($_POST['city_display_settings_nonce']) && wp_verify_nonce($_POST['city_display_settings_nonce'], 'city_display_settings_nonce')) {
            // Featured city
            $featured = isset($_POST['city_featured']) ? '1' : '0';
            update_post_meta($post_id, '_city_featured', $featured);
            
            // City color
            if (isset($_POST['city_color'])) {
                $color = sanitize_hex_color($_POST['city_color']);
                update_post_meta($post_id, '_city_color', $color);
            }
            
            // Show in homepage
            $show_in_homepage = isset($_POST['city_show_in_homepage']) ? '1' : '0';
            update_post_meta($post_id, '_city_show_in_homepage', $show_in_homepage);
            
            // City order
            if (isset($_POST['city_order'])) {
                update_post_meta($post_id, '_city_order', intval($_POST['city_order']));
            }
        }

        // Save banner settings
        if (isset($_POST['city_banner_settings_nonce']) && wp_verify_nonce($_POST['city_banner_settings_nonce'], 'city_banner_settings_nonce')) {
            // Banner image
            if (isset($_POST['city_banner_image'])) {
                $banner_image = intval($_POST['city_banner_image']);
                if ($banner_image > 0) {
                    update_post_meta($post_id, '_city_banner_image', $banner_image);
                } else {
                    delete_post_meta($post_id, '_city_banner_image');
                }
            }

            // Banner title
            if (isset($_POST['city_banner_title'])) {
                $banner_title = sanitize_text_field($_POST['city_banner_title']);
                if (!empty($banner_title)) {
                    update_post_meta($post_id, '_city_banner_title', $banner_title);
                } else {
                    delete_post_meta($post_id, '_city_banner_title');
                }
            }

            // Banner description
            if (isset($_POST['city_banner_description'])) {
                $banner_description = sanitize_textarea_field($_POST['city_banner_description']);
                if (!empty($banner_description)) {
                    update_post_meta($post_id, '_city_banner_description', $banner_description);
                } else {
                    delete_post_meta($post_id, '_city_banner_description');
                }
            }
        }
    }
    
    /**
     * Get city product count
     */
    private function get_city_product_count($city_id) {
        global $wpdb;
        
        $count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(p.ID) 
            FROM {$wpdb->posts} p 
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
            WHERE p.post_type = 'product' 
            AND p.post_status = 'publish' 
            AND pm.meta_key = '_product_city' 
            AND pm.meta_value = %d
        ", $city_id));
        
        return intval($count);
    }
    
    /**
     * Get city category count
     */
    private function get_city_category_count($city_id) {
        global $wpdb;
        
        $count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(t.term_id) 
            FROM {$wpdb->terms} t 
            INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id 
            INNER JOIN {$wpdb->termmeta} tm ON t.term_id = tm.term_id 
            WHERE tt.taxonomy = 'product_cat' 
            AND tm.meta_key = 'category_city' 
            AND tm.meta_value = %d
        ", $city_id));
        
        return intval($count);
    }
    
    /**
     * Get last product date for city
     */
    private function get_last_product_date($city_id) {
        global $wpdb;
        
        $date = $wpdb->get_var($wpdb->prepare("
            SELECT p.post_date 
            FROM {$wpdb->posts} p 
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
            WHERE p.post_type = 'product' 
            AND p.post_status = 'publish' 
            AND pm.meta_key = '_product_city' 
            AND pm.meta_value = %d 
            ORDER BY p.post_date DESC 
            LIMIT 1
        ", $city_id));
        
        return $date;
    }

    /**
     * Banner settings meta box
     */
    public function banner_settings_meta_box($post) {
        wp_nonce_field('city_banner_settings_nonce', 'city_banner_settings_nonce');

        $banner_image = get_post_meta($post->ID, '_city_banner_image', true);
        $banner_title = get_post_meta($post->ID, '_city_banner_title', true);
        $banner_description = get_post_meta($post->ID, '_city_banner_description', true);

        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="city_banner_image"><?php _e('Banner Görseli', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <input type="hidden" id="city_banner_image" name="city_banner_image" value="<?php echo esc_attr($banner_image); ?>" />
                    <button type="button" class="button" id="city_banner_image_button"><?php _e('Görsel Seç', 'glowess-city-ecommerce'); ?></button>
                    <button type="button" class="button" id="city_banner_image_remove" style="margin-left: 10px;"><?php _e('Kaldır', 'glowess-city-ecommerce'); ?></button>
                    <div id="city_banner_image_preview" style="margin-top: 10px;">
                        <?php if ($banner_image): ?>
                            <img src="<?php echo wp_get_attachment_url($banner_image); ?>" style="max-width: 300px; height: auto;" />
                        <?php endif; ?>
                    </div>
                    <p class="description"><?php _e('Ana sayfa banner\'ında kullanılacak görsel. Boş bırakılırsa varsayılan görsel kullanılır.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="city_banner_title"><?php _e('Banner Başlığı', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <input type="text" id="city_banner_title" name="city_banner_title" value="<?php echo esc_attr($banner_title); ?>" class="large-text" />
                    <p class="description"><?php _e('Ana sayfa banner\'ında gösterilecek başlık. Boş bırakılırsa şehir adı kullanılır.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="city_banner_description"><?php _e('Banner Açıklaması', 'glowess-city-ecommerce'); ?></label>
                </th>
                <td>
                    <textarea id="city_banner_description" name="city_banner_description" rows="3" class="large-text"><?php echo esc_textarea($banner_description); ?></textarea>
                    <p class="description"><?php _e('Ana sayfa banner\'ında gösterilecek açıklama. Boş bırakılırsa otomatik açıklama kullanılır.', 'glowess-city-ecommerce'); ?></p>
                </td>
            </tr>
        </table>

        <script>
        jQuery(document).ready(function($) {
            var mediaUploader;

            $('#city_banner_image_button').click(function(e) {
                e.preventDefault();

                if (mediaUploader) {
                    mediaUploader.open();
                    return;
                }

                mediaUploader = wp.media({
                    title: '<?php _e('Banner Görseli Seç', 'glowess-city-ecommerce'); ?>',
                    button: {
                        text: '<?php _e('Seç', 'glowess-city-ecommerce'); ?>'
                    },
                    multiple: false
                });

                mediaUploader.on('select', function() {
                    var attachment = mediaUploader.state().get('selection').first().toJSON();
                    $('#city_banner_image').val(attachment.id);
                    $('#city_banner_image_preview').html('<img src="' + attachment.url + '" style="max-width: 300px; height: auto;" />');
                });

                mediaUploader.open();
            });

            $('#city_banner_image_remove').click(function(e) {
                e.preventDefault();
                $('#city_banner_image').val('');
                $('#city_banner_image_preview').html('');
            });
        });
        </script>
        <?php
    }


}
