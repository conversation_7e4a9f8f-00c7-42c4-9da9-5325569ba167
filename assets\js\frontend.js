/**
 * Glowess City E-commerce Frontend JavaScript
 */

(function($) {
    'use strict';

    // Main object
    var GlowessCityEcommerce = {
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.initCitySelector();
            this.initAjaxFilters();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            // City selector change
            $(document).on('change', '.city-selector, .city-widget-selector', this.handleCityChange);
            
            // City filter form submit
            $(document).on('submit', '.city-selector-form', this.handleFormSubmit);
            
            // AJAX city product loading
            $(document).on('click', '.load-city-products', this.loadCityProducts);
            
            // AJAX city category loading
            $(document).on('click', '.load-city-categories', this.loadCityCategories);
            
            // URL parameter handling
            $(window).on('popstate', this.handlePopState);
        },
        
        /**
         * Initialize city selector
         */
        initCitySelector: function() {
            var $selector = $('.city-selector, .city-widget-selector');
            
            if ($selector.length) {
                // Add loading state support
                $selector.on('change', function() {
                    var $this = $(this);
                    var $form = $this.closest('form');
                    
                    if ($form.length) {
                        $this.prop('disabled', true);
                        $form.addClass('loading');
                    }
                });
            }
        },
        
        /**
         * Initialize AJAX filters
         */
        initAjaxFilters: function() {
            // Check if AJAX filtering is enabled
            if (typeof glowess_city_ajax !== 'undefined' && glowess_city_ajax.enable_ajax === '1') {
                this.enableAjaxFiltering();
            }
        },
        
        /**
         * Handle city selector change
         */
        handleCityChange: function(e) {
            var $this = $(this);
            var citySlug = $this.val();
            var $form = $this.closest('form');
            
            // Update URL without page reload if supported
            if (history.pushState && $form.hasClass('ajax-enabled')) {
                e.preventDefault();
                GlowessCityEcommerce.updateURL(citySlug);
                GlowessCityEcommerce.filterProductsByCity(citySlug);
            }
        },
        
        /**
         * Handle form submit
         */
        handleFormSubmit: function(e) {
            var $form = $(this);
            
            if ($form.hasClass('ajax-enabled')) {
                e.preventDefault();
                var citySlug = $form.find('.city-selector').val();
                GlowessCityEcommerce.updateURL(citySlug);
                GlowessCityEcommerce.filterProductsByCity(citySlug);
            }
        },
        
        /**
         * Handle browser back/forward
         */
        handlePopState: function(e) {
            if (e.originalEvent.state && e.originalEvent.state.city !== undefined) {
                var citySlug = e.originalEvent.state.city;
                $('.city-selector, .city-widget-selector').val(citySlug);
                GlowessCityEcommerce.filterProductsByCity(citySlug);
            }
        },
        
        /**
         * Update URL
         */
        updateURL: function(citySlug) {
            var baseUrl = window.location.origin;
            var newUrl;
            
            if (citySlug) {
                // Use pretty URL format: /antalya/
                newUrl = baseUrl + '/' + citySlug + '/';
            } else {
                // Return to base URL
                newUrl = baseUrl + '/';
            }
            
            history.pushState({city: citySlug}, '', newUrl);
        },
        
        /**
         * Filter products by city via AJAX
         */
        filterProductsByCity: function(citySlug) {
            var $container = $('.products, .woocommerce-products');
            var $resultCount = $('.woocommerce-result-count');
            var $pagination = $('.woocommerce-pagination');
            
            if (!$container.length) {
                return;
            }
            
            // Show loading state
            this.showLoading($container);
            
            // AJAX request
            $.ajax({
                url: glowess_city_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'filter_products_by_city',
                    city: citySlug,
                    nonce: glowess_city_ajax.nonce,
                    category: this.getCurrentCategory(),
                    orderby: this.getCurrentOrderBy(),
                    paged: 1
                },
                success: function(response) {
                    if (response.success) {
                        $container.html(response.data.products);
                        $resultCount.html(response.data.result_count);
                        $pagination.html(response.data.pagination);
                        
                        // Update page title
                        if (response.data.page_title) {
                            document.title = response.data.page_title;
                        }
                        
                        // Trigger event for other scripts
                        $(document).trigger('city_products_filtered', [citySlug, response.data]);
                        
                    } else {
                        GlowessCityEcommerce.showError(response.data || glowess_city_ajax.strings.error);
                    }
                },
                error: function() {
                    GlowessCityEcommerce.showError(glowess_city_ajax.strings.error);
                },
                complete: function() {
                    GlowessCityEcommerce.hideLoading($container);
                }
            });
        },
        
        /**
         * Load city products
         */
        loadCityProducts: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var citySlug = $button.data('city');
            var categorySlug = $button.data('category') || '';
            var limit = $button.data('limit') || 12;
            var $container = $button.data('target') ? $($button.data('target')) : $button.next('.city-products-container');
            
            if (!citySlug || !$container.length) {
                return;
            }
            
            $button.prop('disabled', true).text(glowess_city_ajax.strings.loading);
            
            $.ajax({
                url: glowess_city_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_city_products',
                    city: citySlug,
                    category: categorySlug,
                    limit: limit,
                    nonce: glowess_city_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var html = '';
                        
                        if (response.data.products.length > 0) {
                            html += '<div class="city-products-grid columns-4">';
                            $.each(response.data.products, function(index, product) {
                                html += GlowessCityEcommerce.buildProductHTML(product);
                            });
                            html += '</div>';
                        } else {
                            html = '<p class="city-no-results">' + glowess_city_ajax.strings.no_products + '</p>';
                        }
                        
                        $container.html(html);
                        
                    } else {
                        $container.html('<p class="city-error">' + (response.data || glowess_city_ajax.strings.error) + '</p>');
                    }
                },
                error: function() {
                    $container.html('<p class="city-error">' + glowess_city_ajax.strings.error + '</p>');
                },
                complete: function() {
                    $button.prop('disabled', false).text($button.data('original-text') || 'Ürünleri Yükle');
                }
            });
        },
        
        /**
         * Load city categories
         */
        loadCityCategories: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var citySlug = $button.data('city');
            var $container = $button.data('target') ? $($button.data('target')) : $button.next('.city-categories-container');
            
            if (!citySlug || !$container.length) {
                return;
            }
            
            $button.prop('disabled', true).text(glowess_city_ajax.strings.loading);
            
            $.ajax({
                url: glowess_city_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_city_categories',
                    city: citySlug,
                    nonce: glowess_city_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var html = '';
                        
                        if (response.data.categories.length > 0) {
                            html += '<ul class="city-categories-list">';
                            $.each(response.data.categories, function(index, category) {
                                html += '<li><a href="' + category.permalink + '?city=' + citySlug + '">';
                                html += category.name + ' (' + category.count + ')';
                                html += '</a></li>';
                            });
                            html += '</ul>';
                        } else {
                            html = '<p class="city-no-results">' + glowess_city_ajax.strings.no_categories + '</p>';
                        }
                        
                        $container.html(html);
                        
                    } else {
                        $container.html('<p class="city-error">' + (response.data || glowess_city_ajax.strings.error) + '</p>');
                    }
                },
                error: function() {
                    $container.html('<p class="city-error">' + glowess_city_ajax.strings.error + '</p>');
                },
                complete: function() {
                    $button.prop('disabled', false).text($button.data('original-text') || 'Kategorileri Yükle');
                }
            });
        },
        
        /**
         * Build product HTML
         */
        buildProductHTML: function(product) {
            var html = '<div class="product">';
            html += '<a href="' + product.permalink + '">';
            
            if (product.image) {
                html += '<img src="' + product.image + '" alt="' + product.name + '" />';
            }
            
            html += '<h3>' + product.name + '</h3>';
            
            if (product.price) {
                html += '<span class="price">' + product.price + '</span>';
            }
            
            html += '</a>';
            html += '</div>';
            
            return html;
        },
        
        /**
         * Show loading state
         */
        showLoading: function($container) {
            $container.addClass('city-loading').html('<div class="loading-message">' + glowess_city_ajax.strings.loading + '</div>');
        },
        
        /**
         * Hide loading state
         */
        hideLoading: function($container) {
            $container.removeClass('city-loading');
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            var $container = $('.products, .woocommerce-products');
            if ($container.length) {
                $container.html('<p class="city-error">' + message + '</p>');
            }
        },
        
        /**
         * Get current category
         */
        getCurrentCategory: function() {
            var urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('product_cat') || '';
        },
        
        /**
         * Get current order by
         */
        getCurrentOrderBy: function() {
            var urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('orderby') || 'menu_order';
        },
        
        /**
         * Enable AJAX filtering
         */
        enableAjaxFiltering: function() {
            $('.city-selector-form').addClass('ajax-enabled');
            
            // Prevent default form submission
            $(document).on('submit', '.city-selector-form.ajax-enabled', function(e) {
                e.preventDefault();
            });
        },
        
        /**
         * Utility: Get URL parameter
         */
        getUrlParameter: function(name) {
            var urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        },
        
        /**
         * Utility: Debounce function
         */
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        GlowessCityEcommerce.init();
    });
    
    // Make object globally available
    window.GlowessCityEcommerce = GlowessCityEcommerce;
    
})(jQuery);
